import React from "react";
import { HRAdminOffboardingView } from "src/modules/Employees/Offboarding/OffboardingScreenView";
import Leaves from "src/modules/Leaves/Leaves";
import { NativeLoginPage } from "src/modules/Login/NativeLoginPage";
import { PrivateRoute } from "src/modules/Routing";
import { PATH_CONFIG } from "src/modules/Routing/config";
import TenantsEditView from "src/modules/Tenants/components/TenantsEditView";
import TenantsView from "src/modules/Tenants/components/TenantsView";
import {
  Calendar,
  ComingSoon,
  Dashboard,
  EmployeeOnboarding,
  Leads,
  PageNotFound,
  Profile,
  Reports,
  Tenants,
  Users,
} from "src/pages";
import { Settings } from "src/pages";
import EmployeeAttendance from "src/pages/EmployeeAttendance";
import EmployeeDocuments from "src/pages/EmployeeDocuments";
import EmployeeOffboarding from "src/pages/EmployeeOffboarding";
import EmployeeProfile from "src/pages/EmployeeProfile";
import EmployeeSeperations from "src/pages/EmployeeSeperations";
import Employees from "src/pages/Employees";
import NewJoinees from "src/pages/NewJoinees";
import PerformanceManagement from "src/pages/PerformanceManagement";
import ProjectTracking from "src/pages/ProjectTracking";
import ScreenManagement from "src/pages/ScreenManagement";
import TimeSheets from "src/pages/TimeSheets";

const routingConfig = {
  [PATH_CONFIG.LOGIN.key]: <NativeLoginPage />,
  [PATH_CONFIG.HOME.key]: <PrivateRoute element={<Dashboard />} featureKey={PATH_CONFIG.HOME.key} />,
  [PATH_CONFIG.LEADS.key]: <PrivateRoute element={<Leads />} featureKey={PATH_CONFIG.LEADS.key} />,
  [PATH_CONFIG.EMPLOYEES.key]: <PrivateRoute element={<Employees />} featureKey={PATH_CONFIG.EMPLOYEES.key} />,
  [PATH_CONFIG.TENANTS.key]: <PrivateRoute element={<Tenants />} featureKey={PATH_CONFIG.TENANTS.key} />,
  [PATH_CONFIG.TENANTS_LIST.key]: (
    <PrivateRoute isInternal element={<TenantsView />} featureKey={PATH_CONFIG.TENANTS_LIST.key} />
  ),
  [PATH_CONFIG.TENANTS_EDIT.key]: (
    <PrivateRoute isInternal element={<TenantsEditView />} featureKey={PATH_CONFIG.TENANTS_EDIT.key} />
  ),
  [PATH_CONFIG.REPORTS.key]: <PrivateRoute element={<Reports />} featureKey={PATH_CONFIG.REPORTS.key} />,
  [PATH_CONFIG.USERS.key]: <PrivateRoute element={<Users />} featureKey={PATH_CONFIG.USERS.key} />,
  [PATH_CONFIG.PROFILE.key]: <PrivateRoute element={<Profile />} featureKey={PATH_CONFIG.PROFILE.key} />,
  [PATH_CONFIG.EMPLOYEE_PROFILE.key]: (
    <PrivateRoute element={<EmployeeProfile />} featureKey={PATH_CONFIG.EMPLOYEE_PROFILE.key} isInternal />
  ),
  [PATH_CONFIG.TASKS.key]: <PrivateRoute element={<ComingSoon />} featureKey={PATH_CONFIG.TASKS.key} />,
  [PATH_CONFIG.CALENDAR.key]: <PrivateRoute element={<Calendar />} featureKey={PATH_CONFIG.CALENDAR.key} />,
  [PATH_CONFIG.ONBOARDING.key]: (
    <PrivateRoute element={<EmployeeOnboarding />} featureKey={PATH_CONFIG.ONBOARDING.key} />
  ),
  [PATH_CONFIG.SETTINGS.key]: <PrivateRoute element={<Settings />} featureKey={PATH_CONFIG.SETTINGS.key} />,
  [PATH_CONFIG.SCREEN_MANAGEMENT.key]: (
    <PrivateRoute element={<ScreenManagement />} featureKey={PATH_CONFIG.SCREEN_MANAGEMENT.key} />
  ),
  [PATH_CONFIG.NEWJOINEES.key]: <PrivateRoute element={<NewJoinees />} featureKey={PATH_CONFIG.NEWJOINEES.key} />,
  [PATH_CONFIG.EMPLOYEE_ATTENDANCE.key]: (
    <PrivateRoute element={<EmployeeAttendance />} featureKey={PATH_CONFIG.EMPLOYEE_ATTENDANCE.key} />
  ),
  [PATH_CONFIG.EMPLOYEE_DOCUMENTS.key]: (
    <PrivateRoute element={<EmployeeDocuments />} featureKey={PATH_CONFIG.EMPLOYEE_DOCUMENTS.key} />
  ),
  [PATH_CONFIG.PROJECT_TRACKING.key]: (
    <PrivateRoute element={<ProjectTracking />} featureKey={PATH_CONFIG.PROJECT_TRACKING.key} />
  ),
  [PATH_CONFIG.TIME_SHEETS.key]: <PrivateRoute element={<TimeSheets />} featureKey={PATH_CONFIG.TIME_SHEETS.key} />,
  [PATH_CONFIG.LEAVES.key]: <PrivateRoute element={<Leaves />} featureKey={PATH_CONFIG.LEAVES.key} />,
  [PATH_CONFIG.PEFRORMANCE_MANAGEMENT.key]: (
    <PrivateRoute element={<PerformanceManagement />} featureKey={PATH_CONFIG.PEFRORMANCE_MANAGEMENT.key} />
  ),
  [PATH_CONFIG.PAGE_NOT_FOUND.key]: (
    <PrivateRoute element={<PageNotFound />} featureKey={PATH_CONFIG.PAGE_NOT_FOUND.key} />
  ),
  [PATH_CONFIG.EMPLOYEE_SEPERATIONS.key]: (
    <PrivateRoute element={<HRAdminOffboardingView />} featureKey={PATH_CONFIG.EMPLOYEE_SEPERATIONS.key} />
  ),
  [PATH_CONFIG.EMPLOYEE_SEPERATION.key]: (
    <PrivateRoute element={<EmployeeOffboarding />} featureKey={PATH_CONFIG.EMPLOYEE_SEPERATION.key} />
  ),
  [PATH_CONFIG.EMPLOYEE_OFFBOARDING.key]: (
    <PrivateRoute element={<EmployeeSeperations />} featureKey={PATH_CONFIG.EMPLOYEE_OFFBOARDING.key} />
  ),
};

export const getRoutingConfigByKey = (key: string) => {
  return routingConfig[key];
};

export default routingConfig;
