import { InputAdornment } from "@mui/material";
import React from "react";
import EffiTextField from "../EffiTextField";

type EffiCurrencyProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
  currency?: string;
  [key: string]: any;
};

const EffiCurrency: React.FC<EffiCurrencyProps> = ({
  label,
  required,
  size = "small",
  currency = "INR",
  ...otherProps
}) => {
  return (
    <EffiTextField
      label={label}
      required={required}
      size={size}
      {...otherProps}
      type="number"
      min={0}
      max={100}
      slotProps={{
        input: {
          startAdornment: <InputAdornment position="start">{currency}</InputAdornment>,
        },
      }}
    />
  );
};

export default EffiCurrency;
