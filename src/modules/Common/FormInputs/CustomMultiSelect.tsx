import { Checkbox, FormHelperText, ListItemText, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material";
import React from "react";
import { Option } from "src/app/global";
import { CustomInputLabel } from "./CustomInputLabel";

const CustomMultiSelect = ({
  name,
  onChange,
  options,
  value,
  label,
  required = false,
  ...props
}: {
  name: string;
  onChange: (ev: SelectChangeEvent<string>) => void;
  options: Option<string, any>[];
  value: string[];
  label?: string;
  required?: boolean;
  [propName: string]: any;
}) => {
  return (
    <React.Fragment>
      <CustomInputLabel title={label} required={required} />
      <Select
        id={name}
        value={value as unknown as string}
        labelId={`label-${name}`}
        name={name}
        onChange={onChange}
        displayEmpty
        renderValue={(selected) => {
          if (selected?.length === 0) {
            return (
              <Typography fontSize={16} color="gray">
                {props?.placeholder || "Select multiple entities"}
              </Typography>
            );
          }

          return (selected as unknown as string[]).join(", ");
        }}
        {...props}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value} disabled={option?.disabled}>
            <Checkbox checked={value.includes(option.value)}></Checkbox>
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Select>
      {props?.helperText && props?.error && (
        <FormHelperText error={!!props?.error}>{props?.helperText || ""}</FormHelperText>
      )}
    </React.Fragment>
  );
};

export default CustomMultiSelect;
