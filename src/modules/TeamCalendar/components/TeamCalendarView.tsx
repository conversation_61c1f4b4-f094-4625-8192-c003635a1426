import { MoreVert } from "@mui/icons-material";
import { MenuItem } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { MRT_ColumnDef, MRT_Row } from "material-react-table";
import React, { useMemo, useState } from "react";
import { Actions, MONTHS } from "src/app/constants";
import { createCalendarPayload } from "src/modules/Calendar/Calendar";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import { CalendarEventProps, TeamCalendarProps } from "src/services/api_definitions/calendar";
import calendarServiceAPI from "src/services/calendar.service";
import { getListOfDaysInMonth } from "src/utils/dateUtils";
import AttendanceTrendsModal from "./AttendanceTrendsModal";
import DeleteAttendanceConfig from "./DeleteAttendanceConfig";
import EditAttendanceConfig from "./EditAttendanceConfig";
import ShowMoreEvents from "./ShowMoreEvents";

interface TeamCalendarViewProps {
  selectedDate: string;
}

const defaultColumns: MRT_ColumnDef<TeamCalendarProps>[] = [
  {
    accessorKey: "display_name",
    header: "Team Members",
    size: 247,
    muiTableHeadCellProps: { sx: { backgroundColor: "#17b9a2" } },
    Cell: ({ row }) => {
      const avatarSrc = row?.original?.reportee?.display_pic || "";
      return (
        <EmployeeCellInfo
          name={row?.original?.reportee?.display_name}
          jobTitle={row?.original?.reportee?.job_title}
          displayPic={avatarSrc}
        />
      );
    },
  },
];

const getTeamCalendarRow = (calendarEvent: CalendarEventProps[]) => {
  if (!calendarEvent?.length) {
    return null;
  }

  return <ShowMoreEvents events={calendarEvent} />;
};

export interface DataTableActionProps {
  onClose: () => void;
  selectedRow?: TeamCalendarProps | null;
  teamCalendarDetails?: TeamCalendarProps[];
}

const teamCalendarActions = [
  {
    title: Actions.EDIT_ATTENDANCE_CONFIG,
    id: "edit_attendance_config",
    component: (props: DataTableActionProps) => (
      <EditAttendanceConfig
        employeeCode={props?.selectedRow?.reportee?.employee_code || ""}
        displayName={props?.selectedRow?.reportee?.display_name || ""}
        {...props}
      />
    ),
  },
  {
    title: Actions.DELETE_ATTENDANCE_CONFIG,
    id: "delete_attendance_config",
    component: (props: DataTableActionProps) => (
      <DeleteAttendanceConfig
        employeeCode={props?.selectedRow?.reportee?.employee_code || ""}
        displayName={props?.selectedRow?.reportee?.display_name || ""}
        {...props}
      />
    ),
  },
  {
    title: "Attendance trends",
    id: "view_attendance_trends",
    component: (_props: DataTableActionProps) => <AttendanceTrendsModal {..._props} />,
  },
];

type TeamCalendarActionIds = (typeof teamCalendarActions)[number]["id"];

const TeamCalendarView: React.FC<TeamCalendarViewProps> = ({ selectedDate }) => {
  const [attendanceConfigId, setAttendanceConfigId] = useState<TeamCalendarActionIds | null>();
  const [selectedRow, setSelectedRow] = useState<TeamCalendarProps | null>(null);

  const selectedActionState = attendanceConfigId
    ? teamCalendarActions.find((action) => action.id === attendanceConfigId)
    : null;

  const {
    data = [],
    isLoading,
    isFetching,
  } = useQuery(
    ["get-team-calendar", selectedDate],
    async () => calendarServiceAPI.getTeamCalendarEvents(createCalendarPayload(new Date(selectedDate))),
    {
      refetchOnWindowFocus: false,
    },
  );

  const newAttendancesColumns = useMemo((): MRT_ColumnDef<TeamCalendarProps>[] => {
    if (!selectedDate) {
      return [];
    }
    const [month, year] = selectedDate.split(" ");
    const monthIndex = MONTHS.findIndex((mo) => mo === month);
    return (
      getListOfDaysInMonth(Number(monthIndex + 1), Number(year))?.map((dateString) => ({
        header: dateString,
        Header: () => format(new Date(dateString), "d MMM"),
        muiTableHeadCellProps: {
          align: "center",
        },
        Cell: ({ cell, row }) => {
          const [_index, cellId] = cell.id.split("_");
          const teamCalendar = row?.original?.calendar?.events?.filter(
            (event: CalendarEventProps) => format(event.start, "yyyy-MM-dd") === format(new Date(cellId), "yyyy-MM-dd"),
          );
          return getTeamCalendarRow(teamCalendar);
        },
      })) || []
    );
  }, [data, selectedDate]);

  const onRowActionClick = (id: TeamCalendarActionIds, row: TeamCalendarProps) => {
    setAttendanceConfigId(id as TeamCalendarActionIds);
    setSelectedRow(row);
  };

  // Todo: Refactor this
  const rowActionMenuItems = (row: MRT_Row<TeamCalendarProps>, closeMenu: () => void) => [
    <MenuItem
      key="edit-employee-configuration"
      onClick={() => {
        onRowActionClick("edit_attendance_config", row.original);
        closeMenu();
      }}
    >
      Edit Attendance Config
    </MenuItem>,
    <MenuItem
      key="delete_attendance_config"
      onClick={() => {
        onRowActionClick("delete_attendance_config", row.original);
        closeMenu();
      }}
    >
      Delete Attendance Config
    </MenuItem>,
    <MenuItem
      key="view_attendance_trends"
      onClick={() => {
        onRowActionClick("view_attendance_trends", row.original);
        closeMenu();
      }}
    >
      View Attendance Trends
    </MenuItem>,
  ];

  const onClose = () => {
    setAttendanceConfigId(null);
    setSelectedRow(null);
  };

  return (
    <>
      <DataTable
        enableColumnPinning
        data={data as [] as TeamCalendarProps[]}
        enableRowActions
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: "",
          },
        }}
        state={{
          showSkeletons: isLoading && isFetching && !selectedDate,
          columnPinning: {
            left: ["display_name", "mrt-row-actions"],
          },
        }}
        muiTableProps={{
          sx: {
            backgroundColor: "",
          },
        }}
        muiTableHeadCellProps={{
          sx: {
            border: "0.5px solid #EDEDED",
            backgroundColor: "#F8FFFE",
          },
        }}
        muiTableBodyCellProps={{
          sx: {
            border: "0.5px solid #EDEDED",
            backgroundColor: "#F8F9FC",
            padding: "10px",
          },
        }}
        renderRowActionMenuItems={({ row, closeMenu }) => rowActionMenuItems(row, closeMenu) as any}
        icons={{
          MoreHorizIcon: () => <MoreVert />,
        }}
        positionActionsColumn="first"
        muiTableBodyRowProps={{ hover: false }}
        columns={[...defaultColumns, ...newAttendancesColumns]}
      />
      {selectedActionState?.component({ onClose, selectedRow, teamCalendarDetails: data as [] as TeamCalendarProps[] })}
    </>
  );
};

export default TeamCalendarView;
