import { Divider, Grid2, Paper, Typography } from "@mui/material";
import { Box } from "@mui/system";
import { useStore } from "@tanstack/react-form";
import React, { useEffect } from "react";
import { PayrollComponentV2, PayrollTemplateV2 } from "../../../../services/api_definitions/payroll.service";
import TableActions from "../../../Common/Table/TableActions";

type Props = {
  form: any;
  selectedRow: PayrollTemplateV2;
  selectedComponentTypes: Record<string, PayrollComponentV2[]>;
  setSelectedComponentTypes: React.Dispatch<React.SetStateAction<Record<string, PayrollComponentV2[]>>>;
  allComponents?: PayrollComponentV2[];
};

const DynamicPayrollInputs: React.FC<Props> = ({
  form,
  selectedComponentTypes,
  setSelectedComponentTypes,
}) => {
  const allComponents = useStore(form.store, (state: any) => state.values.components);
  
  const getComponentNameWRTComponentFormulaType = (_component: PayrollComponentV2, index: number) => {
    return allComponents[index]?.component_name;
  };

  useEffect(() => {
    Object.keys(selectedComponentTypes)?.map((_eachComponentType) => {
      selectedComponentTypes[_eachComponentType]?.map((_eachComponent: PayrollComponentV2, idx: number) => {
        console.log({ _eachComponent });
        // const componentToRender = allComponents?.find((eachComponent: PayrollComponentV2) => eachComponent.name === _eachComponent.name);
        form.setFieldValue(`components[${idx}].component_name`, _eachComponent?.name);
        form.setFieldValue(`components[${idx}].component_value`, _eachComponent?.formula.value);
      });
    });
  }, [selectedComponentTypes]);

  const getValueFieldWRTComponentFormulaType = (component: PayrollComponentV2, idx: number) => {
    switch (component.formula.calculation_type) {
      case "Flat":
        return (
          <form.AppField name={`components[${idx}].component_value`}>
            {(field: any) => <field.EffiCurrency label="" currency={component?.currency} />}
          </form.AppField>
        );
      case "Percentage":
        return (
          <form.AppField name={`components[${idx}].component_value`}>
            {(field: any) => (
              <field.EffiPercentageField
                label=""
                endHelperText={`of ${component?.formula?.display_name}`}
              />
            )}
          </form.AppField>
        );
      case "Formula":
        return (
          <form.AppField name={`components[${idx}].component_value`}>
            {(field: any) => <field.EffiCurrency label="" />}
          </form.AppField>
        );
      case "SystemDefined":
        return (
          <Grid2 container justifyContent="center">
            <Grid2 size={12}>
              <Typography
                variant="body1"
                sx={{ fontStyle: "italic", color: "text.secondary" }}
              >
                System Defined
              </Typography>
            </Grid2>
          </Grid2>
        );
      default:
        return null;
    }
  };

  const deleteRow = (field: any, component: PayrollComponentV2, index: number) => {
    console.log({ componentDeleteRow: component, field: field, index });
    field.removeValue(index);
    const updatedSelectedComponentTypes = selectedComponentTypes[component.component_type]?.filter(
      (eachComponent: PayrollComponentV2) => eachComponent.name !== component.name,
    );
    setSelectedComponentTypes((prev: any) => ({
      ...prev,
      [component.component_type]: updatedSelectedComponentTypes,
    }));
  };

  return Object.keys(selectedComponentTypes)?.map((_eachComponentType, index) => (
    <Box key={index} display="flex" flexDirection="column" gap={2} sx={{ margin: "16px 0px" }}>
      <Typography variant="h6" fontWeight={600} fontSize={18}>
        {_eachComponentType}
      </Typography>
      <Divider component={Paper} elevation={3} orientation="horizontal" />
      {selectedComponentTypes[_eachComponentType]?.map((_eachComponent: PayrollComponentV2, idx: number) => (
        <>
          <form.Field name="components" key={index} mode="array">
            {(_subField: any) => (
              <Grid2 container spacing={1}>
                <Grid2 size={5}>
                  <Typography variant="body1">
                    {getComponentNameWRTComponentFormulaType(_eachComponent, idx) || "Component Name"}
                  </Typography>
                </Grid2>
                <Grid2 size={6}>{getValueFieldWRTComponentFormulaType(_eachComponent, idx)}</Grid2>
                <Grid2 size={1}>
                  <TableActions
                    edit={{
                      onClick: () => { },
                      hide: true,
                    }}
                    remove={{
                      onClick: () => deleteRow(_subField, _eachComponent, idx),
                      color: "error",
                      hide: _eachComponent?.mandatory,
                    }}
                    view={{
                      onClick: () => { },
                      hide: true,
                    }}
                  />
                </Grid2>
              </Grid2>
            )}
          </form.Field>
          <Divider  key={index} />
        </>
      ))}
    </Box>
  ));
};

export default DynamicPayrollInputs;
