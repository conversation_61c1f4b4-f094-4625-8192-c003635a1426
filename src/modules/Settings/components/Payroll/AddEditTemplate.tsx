import { Visibility } from "@mui/icons-material";
import { Box, Button, Grid2, IconButton, Tooltip } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { setFullviewMode } from "src/store/slices/app.slice";
import { z } from "zod";
import { useContentHeight } from "../../../../customHooks/useContentHeight";
import { PayrollComponentV2, PayrollTemplateV2 } from "../../../../services/api_definitions/payroll.service";
import payrollService from "../../../../services/payroll.service";
import { useAppForm } from "../../../Common/Form/effiFormContext";
import CompensationComponentTypes from "./CompensationComponentTypes";
import DynamicPayrollInputs from "./DynamicPayrollInputs";
import FixedPayrollInputs from "./FixedPayrollInputs";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";

type Props = {
    isEdit: boolean;
};

export type DefaultFixedFormState = {
    name: string;
    country: string;
    jobTitles: string[];
};

const formSchema = z.object({
    id: z.string().nullish(),
    name: z.string().nonempty({
        message: "Name is required",
    }),
    description: z.string(),
    country: z.string().nonempty({
        message: "Country is required",
    }),
    job_titles: z.array(z.string()).nonempty({
        message: "Job Titles are required",
    }),
    employee_types: z.array(z.string()).nonempty({
        message: "Employee Types are required",
    }),
    aggregates: z.object({
        type: z.enum(["CTC", "GROSS"]),
        value: z.number(),
    }),
    components: z.array(
        z.object({
            id: z.string().nullish(),
            component_name: z.string(),
            component_value: z.number().or(z.string()),
        }),
    ),
});

export const getTemplateDefaultFormState = (selectedRow?: PayrollTemplateV2 | null): DefaultFixedFormState | any => {
    return {
        name: selectedRow?.name || "",
        description: selectedRow?.description || "",
        country: selectedRow?.country || "",
        job_titles: selectedRow?.job_titles || "",
        employee_types: selectedRow?.employee_types || "",
        aggregates: {
            type: "CTC",
            value: 0,
        },
        components:
            selectedRow?.components?.map((eachComponent: PayrollComponentV2) => ({
                ...eachComponent,
                component_name: eachComponent.name,
                component_value: eachComponent.formula.value,
                name: eachComponent.name,
                formula: {
                    ...eachComponent.formula,
                },
            })) || [],
    };
};

const AddEditTemplate: React.FC<PayrollTemplateProps & Props> = ({
    isEdit = false,
    selectedRow,
    setCurrentSelectedMode,
    setSelectedRow,
}) => {
    const height = useContentHeight();
    const dispatch = useAppDispatch();
    const [selectedComponentTypes, setSelectedComponentTypes] = React.useState({});

    const form = useAppForm({
        defaultValues: getTemplateDefaultFormState(selectedRow),
        validators: {
            // onChange: formSchema as any,
            onSubmit: formSchema,
        },
        onSubmit: (params: any) => {
            const formValues = params.value;
            const requestStructure: Partial<PayrollTemplateV2> = {
                name: formValues.name,
                description: formValues.description,
                country: formValues.country,
                job_titles: formValues.job_titles,
                employee_types: formValues.employee_types,
                components: formValues.components.map((eachComponent: any, idx: number) => {
                    const componentFromCompanyType = allComponents?.find(_eachComponent => _eachComponent.name === eachComponent.component_name)
                    return ({
                        ...componentFromCompanyType,
                        sort_order: idx,
                        formula: {
                            ...componentFromCompanyType?.formula,
                            value: eachComponent?.component_value,
                        }
                    });
                }),
            }
            createTemplate.mutate(requestStructure as PayrollTemplateV2);
        }
    });
    const country = useStore(form.store, (state) => state.values.country);

    const { data: allComponents } = useQuery(
        ["get-all-components"],
        async () => {
            const allCompensationComponents: PayrollComponentV2[] =
                await payrollService.getAllCompensationComponents(country);
            allCompensationComponents
                ?.filter((eachComponent: any) => eachComponent.mandatory)
                .reduce((acc: PayrollComponentV2[], eachComponent) => {
                    acc.push(eachComponent);
                    setSelectedComponentTypes((prev: any) => ({
                        ...prev,
                        [eachComponent.component_type]: acc,
                    }));
                    return acc;
                }, []);
            return allCompensationComponents;
        },
        {
            refetchOnWindowFocus: false,
            refetchOnMount: false,
            refetchOnReconnect: false,
            enabled: !!country,
        },
    );

    const createTemplate = useMutation({
        mutationKey: ['create-template'],
        mutationFn: async (payload: PayrollTemplateV2) => payrollService.createTemplate(payload),
        onSuccess: () => {
            goBack();
        },
        onError: (error) => {
            return error;
        },
    });

    const goBack = () => {
        setCurrentSelectedMode(PayrollViewModes.VIEW_ALL);
        setSelectedComponentTypes({});
        dispatch(setFullviewMode(false));
        if (setSelectedRow) {
            setSelectedRow(null);
        }
    };

    useEffect(() => {
        dispatch(setFullviewMode(true));
        if (selectedRow?.components) {
            const defaultSelectedComponentTypes = selectedRow?.components.reduce(
                (acc: any, eachComponent: PayrollComponentV2) => {
                    if (acc[eachComponent.component_type]) {
                        const existingComponentTypes = acc[eachComponent.component_type];
                        existingComponentTypes.push(eachComponent);
                        acc[eachComponent.component_type] = existingComponentTypes;
                        return acc;
                    }
                    acc[eachComponent.component_type] = [eachComponent];
                    return acc;
                },
                {},
            );

            setSelectedComponentTypes(defaultSelectedComponentTypes);
        }
    }, [selectedRow]);

    return (
        <Grid2 container spacing={2}>
            <Grid2 size={3}>
                <CompensationComponentTypes
                    allComponents={allComponents}
                    selectedComponentTypes={selectedComponentTypes}
                    setSelectedComponentTypes={setSelectedComponentTypes}
                />
            </Grid2>
            <Grid2 size={9} position="relative">
                <Box
                    display="flex"
                    flexDirection="column"
                    gap={1}
                    padding={2}
                    sx={{ height: height - 150, maxHeight: height, overflow: "auto" }}
                >
                    <ContentHeader
                        showBackButton
                        goBack={goBack}
                        title="Details"
                        subtitle="Enter country to start adding components"
                        actions={
                            <Tooltip title="Preview Template">
                                <IconButton onClick={() => console.log("Save Clicked")}>
                                    <Visibility color="primary" />
                                </IconButton>
                            </Tooltip>
                        }
                    />
                    <FixedPayrollInputs form={form} isEdit={isEdit} />
                    <DynamicPayrollInputs
                        allComponents={allComponents}
                        form={form}
                        setSelectedComponentTypes={setSelectedComponentTypes}
                        selectedRow={selectedRow as PayrollTemplateV2}
                        selectedComponentTypes={selectedComponentTypes}
                    />
                </Box>
                <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.isValid, state.errorMap]}>
                    {([canSubmit, isSubmitting, isPristine]) =>
                    (
                        <Box position="absolute" textAlign="right" bottom={0} right={0} width="100%" p={2}>
                            <Button sx={{ align: "right" }} variant="contained"
                                disabled={!canSubmit || isSubmitting as boolean || isPristine as boolean}
                                onClick={form.handleSubmit}>Save</Button>
                        </Box>
                    )}
                </form.Subscribe>
            </Grid2>
        </Grid2>
    );
};

export default AddEditTemplate;
