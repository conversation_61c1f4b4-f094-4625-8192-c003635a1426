// useUserDetails.js
import { useQuery } from "@tanstack/react-query";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAuth } from "src/modules/Login/LoginHook";
// import tenantsService from 'src/services/tenants.service';
// import { getCurrentTenantId } from 'src/utils/authUtils';
import userManagementService from "src/services/userManagement.service";
import { setSelectedOrganisation, setUserDetails } from "src/store/slices/userManagement.slice";
import { useAppSelector } from "./useAppSelector";

const useUserDetails = ({ shouldFetch }: { shouldFetch: boolean }) => {
  const { isAuthenticated } = useAuth();
  const { selectedRole } = useAppSelector((state) => state.userManagement);
  const dispatch = useAppDispatch();

  return useQuery(
    ["employee-details", selectedRole],
    async () => {
      const resp = await userManagementService.getUserDetails();
      if (resp) {
        dispatch(setUserDetails(resp));
        if (resp?.organisations?.length > 0) {
          const { organisations = [] } = resp;
          const primaryOrg = organisations.find((org) => org.primary);
          if (primaryOrg) {
            dispatch(setSelectedOrganisation(primaryOrg.name));
          }
        }
      }

      return resp;
    },
    {
      enabled: isAuthenticated && shouldFetch,
      retryOnMount: false,
      refetchInterval: false,
      refetchOnWindowFocus: false,
    },
  );
};

export { useUserDetails };
