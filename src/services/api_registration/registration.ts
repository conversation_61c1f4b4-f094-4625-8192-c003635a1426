export default {
  PROFILE: {
    paths: {
      "get-employee-details": "/employee/details",
      "get-search-terms": "/search/omniSuggestion",
      "add-employee-details": "/employee/create",
      "update-employee-personal-details": "/employee/update/personal",
      "update-employee-professional-details": "/employee/update/professional",
      "update-employee-current-address": "/employee/address/current",
      "update-employee-permanent-address": "/employee/address/permanent",
      "update-employee-work-experience": "/employee/work_experience",
      "update-employee-bank-account": "/employee/bank_account",
      "update-employee-emergency-contacts": "/employee/emergency_contacts",
      "update-employee-education-details": "/employee/education_details",
      "update-employee-family-details": "/employee/dependents",
      "upload-display-pic": "/employee/update/display_pic",
      "get-employee-journey": "/employee/journey",
      "get-employee-journey-admin": "/admin/employee/journey",
    },
  },
  CALENDAR: {
    paths: {
      "get-my-calendar": "/calendar/me",
      "get-team-calendar": "/calendar/team",
    },
  },
  LEADS: {
    paths: {
      "get-tenant-leads": "/tenant/leads",
      "post-generate-magiclink": "/tenant/magiclink/generate",
    },
  },
  AUTH: {
    paths: {
      "get-authorised-screens": "/role_screen",
      "get-auth-tenant-details": "/tenant/open",
    },
  },
  EMPLOYEES: {
    paths: {
      "get-employees-details": "/admin/employees/all",
      "download-sample-template": "/employee/template",
      "upload-employees-details": "/employee/import",
      "get-ifsc-bank-details": "/api/bank_details/ifsc",
      "get-job-title-details": "/job_title/details",
      "get-all-institutes": "/education/institute/all",
      "get-all-degrees": "/education/degree/all",
      "get-address-from-pincode": "/location/open/pincode",
      "create-new-employee": "/employee/create",
      "get-work-role-hierarcy": "/work_role/hierarchy",
      "get-candidate-details": "/candidate/details",
      "update-candidate": "/candidate/update",
      "draft-candidate": "/candidate/draft",
      "terminate-employee": "/employee/terminate",
      "get-employee-data": "/admin/employee",
      "update-employee-data": "/admin/update/professional",
      "offboarding-details": "/offboarding/details",
      "initiate-offboarding": "/offboarding/initiate",
      "offboarding-requests": "/offboarding/requests",
      "approve-offboarding": "/offboarding/request/update",
      "offboarding-overview": "/offboarding/overview",
      "rescind-offboarding": "/offboarding/rescind",
      "change-date-of-confirmation": "/admin/:employeeId/date_of_confirmation",
      "export-employees": "/employee/export",
      "save-document": "/employee/documents",
    },
  },
  DOCUMENTS: {
    paths: {
      "create-company-document": "/document/policy/create",
      "update-company-document": "/document/policy/update",
      "delete-company-document": "/document/policy/delete",
      "get-employee-documents": "/document/employee/all",
      "get-company-documents": "/document/policy/all",
      "upload-profile-picture": "/document/upload",
    },
  },
  NEWHIRES: {
    paths: {
      "get-all-candidates": "/candidate/all",
      "get-candidates-details": "/candidate/details",
      "create-new-candidate": "/candidate/create",
      "update-candidate-details": "/candidate/update",
      "post-reject-candidate": "/candidate/reject",
      "post-resend-link": "/candidate/resend_link",
    },
  },
  USER_MANAGEMENT: {
    paths: {
      "get-user-saved-settings": "/user_settings/details",
      "insert-user-settings": "/user_settings",
      "user-basic-details": "/user/details",
    },
  },
  ROLE_MANAGEMENT: {
    paths: {
      "create-user-role": "/role/custom/create",
      "delete-user-role": "/role/custom/delete",
      "update-user-role": "/role/custom/update",
      "get-user-roles": "/role/details",
      "create-user-roles": "/role/custom/create",
      "update-user-roles": "/role/custom/update",
      "delete-user-roles": "/role/custom/delete",
      "get-all-role-screens": "/role_screen/all",
      "upsert-acl-role-screen": "/role_screen/update",
      "get-user-allowed-roles": "/role/list",
    },
  },
  USER_ROLE_MANAGEMENT: {
    paths: {
      "get-all-user-roles": "/user_role/all",
      "update-user-role": "/user_role/update",
      "delete-user-role": "/user_role/delete",
      "create-user-role": "/user_role/create",
      "get-all-roles": "/role/all",
    },
  },
  MASTER_DATA: {
    paths: {
      "get-master-data": "/enums",
    },
  },
  TENANTS: {
    paths: {
      "get-tenant-details": "/tenant/all",
      "get-tenant": "/tenant",
      "get-cost-center-details": "/cost_center/details",
      "get-work-role-details": "/work_role/details",
      "download-sample-template": "/work_role/template",
      "download-job-title-template": "/job_title/template",
      "upload-work-roles": "/work_role/import",
      "upload-job-titles": "/job_title/import",
      "get-all-job-titles": "/job_title/all",
      "set-job-title-details": "/job_title/create",
      "update-job-title-details": "/job_title/update",
      "delete-job-title-details": "/job_title/delete",
      "create-cost-centers": "/cost_center/create",
      "tenant-update": "/tenant/update",
      "get-selected-tenant-details": "/tenant?id=:tenantId",
      "get-all-business-units": "/business_unit/all",
      "get-business-unit-details": "/business_unit/details",
      "set-business-unit-details": "/business_unit/create",
      "update-business-unit-details": "/business_unit/update",
      "delete-business-unit-details": "/business_unit/delete",
      "get-all-departments": "/department/all",
      "set-department-details": "/department/create",
      "get-department-details": "/department/details?business_unit=:businessUnit",
      "update-department-details": "/department/update",
      "delete-department-details": "/department/delete",
      "get-all-sub-departments": "/sub_department/all",
      "set-sub-department-details": "/sub_department/create",
      "update-sub-department-details": "/sub_department/update",
      "delete-sub-department-details": "/sub_department/delete",
      "get-sub-department-details": "/sub_department/details?department=:department",
      "get-all-teams": "/team/all",
      "set-team-details": "/team/create",
      "update-team-details": "/team/update",
      "delete-team-details": "/team/delete",
      "get-all-job-families": "/job_family/all",
      "set-job-family-details": "/job_family/create",
      "update-job-family-details": "/job_family/update",
      "delete-job-family-details": "/job_family/delete",
      "get-employee-id-config": "/employee_codegen_config/details",
      "set-employee-id-config": "/employee_codegen_config/create",
      "update-employee-id-config": "/employee_codegen_config/update",
      "get-all-work-roles": "/work_role/details?tenant_id=:tenantId",
      "get-attendance-config": "/attendance/config/details",
      "get-all-attendance-config": "/attendance/config/all",
      "create-attendance-config": "/attendance/organisation/config/create",
      "update-attendance-config": "/attendance/organisation/config/update",
      "update-tenant": "/tenant/update",
      "add-work-role": "/work_role/create",
      "delete-work-role": "/work_role/delete",
      "delete-tenant": "/tenant/delete",
      "get-tenant-integrations": "/tenant/integrations",
      "get-employee-attendance-details": "/attendance/employee/config/details",
      "upsert-employee-attendance-details": "/attendance/employee/config/update",
      "delete-employee-attendance-details": "/attendance/employee/config/delete",
      "get-tenant-subscriptions": "/subscription/details",
      "create-tenant-subscription": "/subscription/create",
      "cancel-tenant-subscription": "/subscription/cancel",
    },
  },
  SCREEN_MANAGEMENT: {
    paths: {
      "get-screen-uris": "/screen_uri/all",
      "get-all-screens": "/screen/all",
      "get-all-uris": "/uri/all",
      "update-screen-uri": "/screen_uri/update",
      "create-screen": "/screen/create",
      "update-screen": "/screen/update",
      "delete-screen": "/screen/delete",
    },
  },
  HOLIDAY: {
    paths: {
      "get-organisation-holidays": "/organisation_holiday/all",
      "get-holidays": "/holiday/all",
      "create-organisation-holiday": "/organisation_holiday/create",
      "update-organisation-holiday": "/organisation_holiday/update",
      "create-holiday": "/holiday/custom/create",
      "delete-holiday": "/organisation_holiday/delete",
      "get-leave-details": "/leave/details",
      "create-leave-type": "/leave/custom/create",
      "update-leave-type": "/leave/custom/update",
      "delete-leave-type": "/leave/custom/delete",
      "get-leave-policies": "/leave/policy/details",
      "create-leave-policies": "/leave/policy/create",
      "update-leave-policies": "/leave/policy/update",
      "delete-leave-policy": "/leave/policy/delete",
      "download-org-pdf": "/organisation_holiday/pdf",
      "upload-leaves": "/leave/import",
      "download-template": "/leave/template",
    },
  },
  ORGANISATIONS: {
    paths: {
      "get-organisations": "/organisation/all",
      "update-organisation": "/organisation/update",
      "create-organisation": "/organisation/create",
      "delete-organisation": "/organisation/delete",
    },
  },
  AWS_S3: {
    paths: {
      "get-presigned-url": "/document/upload_pre_signed_url",
      "get-public-presigned-url": "/document/upload_public_pre_signed_url",
      "upload-logo": "/document/upload/logo",
      "download-presigned-url": "/document/download_pre_signed_url",
      "get-document-preview-url": "/document/preview",
    },
  },
  TIMESHEETS: {
    paths: {
      "get-timesheets": "/time_sheet/all",
      "export-timesheets": "/time_sheet/export",
    },
  },
  DASHBOARD: {
    paths: {
      "get-org-events": "/calendar/anniversary/all",
      "get-team-status": "/employee/team/status",
      "get-attendance-details": "/attendance/details",
      "check-in": "/attendance/check-in/:location",
      "check-out": "/attendance/check-out",
      "get-holiday-list": "/holiday/:year/list",
      "get-new-joiners": "/candidate/all/today",
      "get-employee-seperations": "/employee/separation/all/today",
    },
  },
  LEAVES: {
    paths: {
      "get-leave-summary": "/leave/summary",
      "get-leave-requests": "/leave/requests",
      "apply-leave": "/leave/request/apply",
      "edit-leave-request": "/leave/request/update",
      "delete-leave-request": "/leave/request/delete",
      "leave-request-approval": "/leave/approvals",
      "approve-leave-request": "/leave/request/approve",
      "reject-leave-request": "/leave/request/reject",
      "get-leave-transactions": "/leave/transactions",
      "approve-batch-leave-requests": "/leave/request/bulk/approve",
      "reject-batch-leave-requests": "/leave/request/bulk/reject",
    },
  },
  EMPLOYEE_ATTENDANCE: {
    paths: {
      "get-attendance-activity-logs": "/attendance/activity_logs",
      "get-regularization-requests": "/attendance/regularisation/requests",
      "get-regularization-approvals": "/attendance/regularisation/approvals",
      "apply-regularisation": "/attendance/regularise",
      "approve-regularisation": "/attendance/regularisation/approve",
      "reject-regularisation": "/attendance/regularisation/reject",
      "bulk-approve-regularisation": "/attendance/regularisation/bulk/approve",
      "bulk-reject-regularisation": "/attendance/regularisation/bulk/reject",
    },
  },
  GENERAL: {
    paths: {
      "get-country-list": "/location/open/country/list",
    },
  },
  NOTIFICATIONS: {
    paths: {
      "register-notification-token": "/push-notification/registration-token",
      "get-all-notification": "/notification/all?number_of_days=:numberOfDays",
      "mark-notification-as-read": "/notification/:notificationId/read",
      "mark-all-notification-as-read": "/notification/read",
      "get-unread-notification": "/notification/unread",
      "push-notification": "push-notification/test/TASK_DEADLINE",
      "update-notification-settings": "/notification/preference/update",
      "get-notification-settings": "/notification/preference",
      "get-unread-notification-count": "/notification/unread/count",
      "clear-all-notifications": "/notification/clear",
    },
  },
  PERFORMANCE_MANAGEMENT: {
    paths: {
      // settings
      "get-review-cycle-config-details": "/performance_management/review_cycle/all",
      "update-review-cycle-config-details": "/performance_management/review_cycle/update",
      "create-review-cycle-config-details": "/performance_management/review_cycle/create",
      "get-ratings-configuration": "/performance_management/ratings/all",
      "create-ratings-configuration": "/performance_management/ratings/create",
      "update-ratings-configuration": "/performance_management/ratings/update",
      "get-manager-goal-objectives": "/performance_management/manager/goal_objectives",
      "enable-goal-setting-for-employee": "/performance_management/goal/enable",
      "get-employee-goal-setting-status": "/performance_management/goal_setting/status",
      "get-employee-performance-review-status": "/performance_management/review/status",

      // Employee management
      "get-goals": "/performance_management/goals",
      "update-goal": "/performance_management/goal/update",
      "approve-goal": "/performance_management/goal/approve",
      "sendback-goal": "/performance_management/goal/send_back",

      // Manager Flow
      "get-goal-requests": "/performance_management/goal/requests",
      "approve-goal-request": "/performance_management/goal/approve",
      "sendback-goal-request": "/performance_management/goal/send_back",

      // Employee Performance Reviews
      "get-employee-reviews": "/performance_management/reviews",
      "get-employee-review-requests": "/performance_management/review/requests/:reviewerType",
      "update-performance-review": "/performance_management/review/:reviewerType",
      "get-peer-nominations": "/performance_management/peer_nomination",
      "update-peer-nominations": "/performance_management/peer_nomination/update",
      "get-peer-nomination-requests": "/performance_management/peer_nomination/requests",
      "approve-peer-nomination": "/performance_management/peer_nomination/approve",
      "send-back-performance-review": "/performance_management/review/:reviewerType/send_back",

      // Resource Allocation -
      "get-resource-allocation": "/performance_management/goal/resource_allocation",
    },
  },
  OFFBOARDING: {
    paths: {
      "notice-period-in-days": "/offboarding/notice_period",
      "get-employee-seperations": "/employee/separation/all",
      "employee-probation-period": "/onboarding/probation_period",
    },
  },
  PAYROLL: {
    paths: {
      "get-payroll-templates": "/compensation/template/all",
      "add-payroll-template": "/compensation/template/create",
      "update-payroll-template": "/compensation/template/update",
      "delete-payroll-template": "/compensation/template/delete",
      "payroll-template-details": "/compensation/template/details",
      "all-payroll-components": "/compensation/component/all",
    },
  },
  USER: {
    paths: {
      "get-user-details": "/user/me",
      "refresh-token": "/auth/refresh-token",
      "sign-out": "/auth/logout",
      "custom-login": "/auth/login",
      "generate-otp": "/open/otp/generate",
      "verify-otp": "/auth/register",
    },
  },
  PROJECT_TRACKING: {
    paths: {
      "get-tasks": "/project_tracking/tasks/all",
      "add-task": "/project_tracking/task/create",
      "update-task": "/project_tracking/task/update",
      "delete-task": "/project_tracking/task/:taskId/delete",
      "clock-in": "/project_tracking/task/:taskId/clock-in",
      "clock-out": "/project_tracking/task/:taskId/clock-out",
      "mark-complete": "/project_tracking/task/:taskId/mark-complete",
      "reopen-task": "/project_tracking/task/:taskId/re-open",
    },
  },
};
