export type UserSettings = {
  defaultEmployeeRole?: string;
  defaultEmployeeOrg?: string;
  quickActions?: Record<string, string[]>;
};

export type UserSettingsResponse = {
  settings: UserSettings;
};

export type OrganisationDetails = {
  name: string;
  status: string;
  logo: string;
  primary: boolean;
  addresses: {
    address_line1: string;
    address_line2: string;
    city: string;
    state: string;
    country: string;
    zip_code: string;
    display_address: string;
    primary?: boolean;
    geofence_enabled: boolean;
  }[];
};

export type UserDetails = {
  organisations: OrganisationDetails[];
  employee_code: string;
  employment_status: string;
  display_name: string;
  is_manager: boolean;
  email: string;
  display_pic?: string;
};
